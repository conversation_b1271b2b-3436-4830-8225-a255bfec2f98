import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Data estimasi biaya haji
const BIAYA_HAJI_ESTIMATES = {
  reguler: 35000000,
  plus: 50000000,
  khusus: 75000000,
};

export default function SimulasiScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [targetBiaya, setTargetBiaya] = useState('35000000');
  const [tabunganSaatIni, setTabunganSaatIni] = useState('12500000');
  const [setoranPerBulan, setSetoranPerBulan] = useState('500000');
  const [selectedPackage, setSelectedPackage] = useState('reguler');

  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const calculateSimulation = () => {
    const target = Number(targetBiaya) || 0;
    const current = Number(tabunganSaatIni) || 0;
    const monthly = Number(setoranPerBulan) || 0;

    if (target <= 0 || monthly <= 0) {
      return null;
    }

    const remaining = Math.max(0, target - current);
    const monthsNeeded = monthly > 0 ? Math.ceil(remaining / monthly) : 0;
    const yearsNeeded = Math.floor(monthsNeeded / 12);
    const remainingMonths = monthsNeeded % 12;

    return {
      target,
      current,
      monthly,
      remaining,
      monthsNeeded,
      yearsNeeded,
      remainingMonths,
      isCompleted: current >= target,
    };
  };

  const simulation = calculateSimulation();

  const handlePackageSelect = (packageType: keyof typeof BIAYA_HAJI_ESTIMATES) => {
    setSelectedPackage(packageType);
    setTargetBiaya(BIAYA_HAJI_ESTIMATES[packageType].toString());
  };

  const handleSaveTarget = () => {
    if (simulation) {
      Alert.alert(
        'Target Disimpan',
        `Target biaya haji Anda sebesar ${formatRupiah(simulation.target)} telah disimpan.`,
        [{ text: 'OK' }]
      );
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Simulasi & Target
          </Text>
          <Text style={[styles.subtitle, { color: colors.text }]}>
            Rencanakan tabungan haji Anda
          </Text>
        </View>

        {/* Package Selection */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Pilih Paket Haji
          </Text>
          
          {Object.entries(BIAYA_HAJI_ESTIMATES).map(([key, value]) => (
            <TouchableOpacity
              key={key}
              style={[
                styles.packageCard,
                {
                  backgroundColor: selectedPackage === key ? colors.backgroundTertiary : colors.cardBackground,
                  borderColor: selectedPackage === key ? colors.primary : colors.cardBorder,
                  borderWidth: 1,
                }
              ]}
              onPress={() => handlePackageSelect(key as keyof typeof BIAYA_HAJI_ESTIMATES)}
            >
              <View style={styles.packageInfo}>
                <Text style={[styles.packageName, { color: colors.text }]}>
                  Haji {key.charAt(0).toUpperCase() + key.slice(1)}
                </Text>
                <Text style={[styles.packagePrice, { color: colors.primary }]}>
                  {formatRupiah(value)}
                </Text>
              </View>
              {selectedPackage === key && (
                <IconSymbol name="chevron.right" size={20} color={colors.tint} />
              )}
            </TouchableOpacity>
          ))}
        </View>

        {/* Input Section */}
        <View style={styles.section}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            Detail Simulasi
          </Text>
          
          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>
              Target Biaya Haji (Rp)
            </Text>
            <TextInput
              style={[styles.input, {
                borderColor: colors.secondary,
                color: colors.text,
                backgroundColor: colors.backgroundSecondary
              }]}
              value={targetBiaya}
              onChangeText={setTargetBiaya}
              keyboardType="numeric"
              placeholder="35000000"
              placeholderTextColor={colors.textMuted}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>
              Tabungan Saat Ini (Rp)
            </Text>
            <TextInput
              style={[styles.input, {
                borderColor: colors.secondary,
                color: colors.text,
                backgroundColor: colors.backgroundSecondary
              }]}
              value={tabunganSaatIni}
              onChangeText={setTabunganSaatIni}
              keyboardType="numeric"
              placeholder="0"
              placeholderTextColor={colors.textMuted}
            />
          </View>

          <View style={styles.inputGroup}>
            <Text style={[styles.inputLabel, { color: colors.text }]}>
              Setoran per Bulan (Rp)
            </Text>
            <TextInput
              style={[styles.input, {
                borderColor: colors.secondary,
                color: colors.text,
                backgroundColor: colors.backgroundSecondary
              }]}
              value={setoranPerBulan}
              onChangeText={setSetoranPerBulan}
              keyboardType="numeric"
              placeholder="500000"
              placeholderTextColor={colors.textMuted}
            />
          </View>
        </View>

        {/* Results */}
        {simulation && (
          <View style={styles.section}>
            <Text style={[styles.sectionTitle, { color: colors.text }]}>
              Hasil Simulasi
            </Text>
            
            <View style={[styles.resultCard, { backgroundColor: colors.tint }]}>
              {simulation.isCompleted ? (
                <View style={styles.completedResult}>
                  <IconSymbol name="target" size={32} color="white" />
                  <Text style={styles.completedTitle}>
                    🎉 Target Tercapai!
                  </Text>
                  <Text style={styles.completedText}>
                    Tabungan Anda sudah mencukupi untuk biaya haji
                  </Text>
                </View>
              ) : (
                <>
                  <View style={styles.resultRow}>
                    <Text style={styles.resultLabel}>Sisa yang dibutuhkan:</Text>
                    <Text style={styles.resultValue}>
                      {formatRupiah(simulation.remaining)}
                    </Text>
                  </View>
                  
                  <View style={styles.resultRow}>
                    <Text style={styles.resultLabel}>Waktu yang dibutuhkan:</Text>
                    <Text style={styles.resultValue}>
                      {simulation.yearsNeeded > 0 && `${simulation.yearsNeeded} tahun `}
                      {simulation.remainingMonths > 0 && `${simulation.remainingMonths} bulan`}
                      {simulation.monthsNeeded === 0 && 'Target sudah tercapai'}
                    </Text>
                  </View>
                  
                  <View style={styles.resultRow}>
                    <Text style={styles.resultLabel}>Total setoran bulanan:</Text>
                    <Text style={styles.resultValue}>
                      {formatRupiah(simulation.monthly)}
                    </Text>
                  </View>
                </>
              )}
            </View>

            {/* Tips */}
            <View style={[styles.tipsCard, { backgroundColor: colors.background }]}>
              <Text style={[styles.tipsTitle, { color: colors.text }]}>
                💡 Tips Menabung
              </Text>
              <Text style={[styles.tipsText, { color: colors.text }]}>
                • Konsisten menabung setiap bulan{'\n'}
                • Manfaatkan bonus atau THR untuk setoran tambahan{'\n'}
                • Pertimbangkan investasi syariah untuk hasil optimal{'\n'}
                • Daftar porsi haji sedini mungkin
              </Text>
            </View>

            <TouchableOpacity 
              style={[styles.saveButton, { backgroundColor: colors.tint }]}
              onPress={handleSaveTarget}
            >
              <IconSymbol name="target" size={20} color="white" />
              <Text style={styles.saveButtonText}>Simpan Target</Text>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  subtitle: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 4,
  },
  section: {
    padding: 20,
    paddingTop: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  packageCard: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  packageInfo: {
    flex: 1,
  },
  packageName: {
    fontSize: 16,
    fontWeight: '500',
  },
  packagePrice: {
    fontSize: 14,
    fontWeight: '600',
    marginTop: 2,
  },
  inputGroup: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  resultCard: {
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
  },
  resultRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  resultLabel: {
    color: 'white',
    fontSize: 14,
    flex: 1,
  },
  resultValue: {
    color: 'white',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'right',
    flex: 1,
  },
  completedResult: {
    alignItems: 'center',
  },
  completedTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 8,
  },
  completedText: {
    color: 'white',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
    opacity: 0.9,
  },
  tipsCard: {
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tipsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 8,
  },
  tipsText: {
    fontSize: 14,
    lineHeight: 20,
    opacity: 0.8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 12,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
  },
  saveButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
});
