import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Dummy data untuk riwayat setoran
const DUMMY_RIWAYAT = [
  { id: 1, tanggal: '2024-06-20', jumlah: 500000, keterangan: 'Setoran rutin' },
  { id: 2, tanggal: '2024-06-15', jumlah: 750000, keterangan: 'Bonus kerja' },
  { id: 3, tanggal: '2024-06-10', jumlah: 600000, keterangan: 'Setoran rutin' },
  { id: 4, tanggal: '2024-06-05', jumlah: 500000, keterangan: 'Setoran rutin' },
  { id: 5, tanggal: '2024-05-30', jumlah: 1000000, keterangan: 'THR' },
  { id: 6, tanggal: '2024-05-25', jumlah: 500000, keterangan: 'Setoran rutin' },
];

export default function TabunganScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [riwayatSetoran, setRiwayatSetoran] = useState(DUMMY_RIWAYAT);
  const [modalVisible, setModalVisible] = useState(false);
  const [jumlahSetoran, setJumlahSetoran] = useState('');
  const [keterangan, setKeterangan] = useState('');

  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatTanggal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleTambahSetoran = () => {
    if (!jumlahSetoran || isNaN(Number(jumlahSetoran))) {
      Alert.alert('Error', 'Masukkan jumlah setoran yang valid');
      return;
    }

    const newSetoran = {
      id: riwayatSetoran.length + 1,
      tanggal: new Date().toISOString().split('T')[0],
      jumlah: Number(jumlahSetoran),
      keterangan: keterangan || 'Setoran baru'
    };

    setRiwayatSetoran([newSetoran, ...riwayatSetoran]);
    setJumlahSetoran('');
    setKeterangan('');
    setModalVisible(false);
    
    Alert.alert(
      'Berhasil!', 
      `Setoran sebesar ${formatRupiah(Number(jumlahSetoran))} berhasil ditambahkan`
    );
  };

  const totalTabungan = riwayatSetoran.reduce((total, item) => total + item.jumlah, 0);

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          Tabungan Haji
        </Text>
        <TouchableOpacity 
          style={[styles.addButton, { backgroundColor: colors.tint }]}
          onPress={() => setModalVisible(true)}
        >
          <IconSymbol name="plus.circle.fill" size={20} color="white" />
        </TouchableOpacity>
      </View>

      {/* Total Tabungan Card */}
      <View style={[styles.totalCard, { backgroundColor: colors.tint }]}>
        <Text style={styles.totalLabel}>Total Tabungan</Text>
        <Text style={styles.totalAmount}>{formatRupiah(totalTabungan)}</Text>
        <Text style={styles.totalCount}>{riwayatSetoran.length} kali setoran</Text>
      </View>

      {/* Riwayat Setoran */}
      <View style={styles.historySection}>
        <Text style={[styles.historyTitle, { color: colors.text }]}>
          Riwayat Setoran
        </Text>
        
        <ScrollView showsVerticalScrollIndicator={false}>
          {riwayatSetoran.map((item) => (
            <View key={item.id} style={[styles.historyItem, { backgroundColor: colors.background }]}>
              <View style={styles.historyLeft}>
                <View style={[styles.historyIcon, { backgroundColor: colors.tint }]}>
                  <IconSymbol name="banknote.fill" size={16} color="white" />
                </View>
                <View>
                  <Text style={[styles.historyKeterangan, { color: colors.text }]}>
                    {item.keterangan}
                  </Text>
                  <Text style={[styles.historyTanggal, { color: colors.text }]}>
                    {formatTanggal(item.tanggal)}
                  </Text>
                </View>
              </View>
              <Text style={[styles.historyAmount, { color: colors.tint }]}>
                {formatRupiah(item.jumlah)}
              </Text>
            </View>
          ))}
        </ScrollView>
      </View>

      {/* Modal Tambah Setoran */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Tambah Setoran
              </Text>
              <TouchableOpacity 
                onPress={() => setModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Jumlah Setoran (Rp)
              </Text>
              <TextInput
                style={[styles.input, { 
                  borderColor: colors.tint, 
                  color: colors.text 
                }]}
                placeholder="Masukkan jumlah setoran"
                placeholderTextColor={colors.text + '80'}
                value={jumlahSetoran}
                onChangeText={setJumlahSetoran}
                keyboardType="numeric"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Keterangan (Opsional)
              </Text>
              <TextInput
                style={[styles.input, { 
                  borderColor: colors.tint, 
                  color: colors.text 
                }]}
                placeholder="Contoh: Setoran rutin, bonus, dll"
                placeholderTextColor={colors.text + '80'}
                value={keterangan}
                onChangeText={setKeterangan}
              />
            </View>

            <TouchableOpacity 
              style={[styles.submitButton, { backgroundColor: colors.tint }]}
              onPress={handleTambahSetoran}
            >
              <Text style={styles.submitButtonText}>Tambah Setoran</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  totalCard: {
    margin: 20,
    padding: 20,
    borderRadius: 16,
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  totalLabel: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  totalAmount: {
    color: 'white',
    fontSize: 28,
    fontWeight: 'bold',
    marginVertical: 4,
  },
  totalCount: {
    color: 'white',
    fontSize: 12,
    opacity: 0.8,
  },
  historySection: {
    flex: 1,
    paddingHorizontal: 20,
  },
  historyTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    marginBottom: 8,
    borderRadius: 12,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  historyLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  historyIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  historyKeterangan: {
    fontSize: 14,
    fontWeight: '500',
  },
  historyTanggal: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  historyAmount: {
    fontSize: 14,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    borderRadius: 16,
    padding: 20,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
