import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React from 'react';
import { View, ViewProps } from 'react-native';

interface CardProps extends ViewProps {
  variant?: 'default' | 'elevated' | 'outlined' | 'hero';
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  margin?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  borderRadius?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

export function Card({
  variant = 'default',
  padding = 'md',
  margin = 'none',
  borderRadius = 'lg',
  style,
  children,
  ...props
}: CardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getVariantStyle = () => {
    switch (variant) {
      case 'default':
        return {
          backgroundColor: colors.cardBackground,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 2,
          elevation: 1,
        };
      case 'elevated':
        return {
          backgroundColor: colors.cardBackground,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.1,
          shadowRadius: 6,
          elevation: 4,
        };
      case 'outlined':
        return {
          backgroundColor: colors.cardBackground,
          borderWidth: 1,
          borderColor: colors.cardBorder,
        };
      case 'hero':
        return {
          backgroundColor: colors.primary,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.15,
          shadowRadius: 16,
          elevation: 8,
        };
      default:
        return {
          backgroundColor: colors.cardBackground,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 1 },
          shadowOpacity: 0.05,
          shadowRadius: 2,
          elevation: 1,
        };
    }
  };

  const getPaddingStyle = () => {
    switch (padding) {
      case 'none':
        return { padding: 0 };
      case 'sm':
        return { padding: 8 };
      case 'md':
        return { padding: 16 };
      case 'lg':
        return { padding: 20 };
      case 'xl':
        return { padding: 24 };
      default:
        return { padding: 16 };
    }
  };

  const getMarginStyle = () => {
    switch (margin) {
      case 'none':
        return { margin: 0 };
      case 'sm':
        return { margin: 8 };
      case 'md':
        return { margin: 16 };
      case 'lg':
        return { margin: 20 };
      case 'xl':
        return { margin: 24 };
      default:
        return { margin: 0 };
    }
  };

  const getBorderRadiusStyle = () => {
    switch (borderRadius) {
      case 'sm':
        return { borderRadius: 4 };
      case 'md':
        return { borderRadius: 12 };
      case 'lg':
        return { borderRadius: 16 };
      case 'xl':
        return { borderRadius: 20 };
      case '2xl':
        return { borderRadius: 24 };
      default:
        return { borderRadius: 16 };
    }
  };

  return (
    <View
      style={[
        getVariantStyle(),
        getPaddingStyle(),
        getMarginStyle(),
        getBorderRadiusStyle(),
        style,
      ]}
      {...props}
    >
      {children}
    </View>
  );
}

// Convenience components for common card types
export const HeroCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="hero" {...props} />
);

export const ElevatedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="elevated" {...props} />
);

export const OutlinedCard = (props: Omit<CardProps, 'variant'>) => (
  <Card variant="outlined" {...props} />
);
