import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
  Alert,
  Modal,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Dummy user data
const DUMMY_USER = {
  nama: '<PERSON>',
  email: '<EMAIL>',
  telepon: '081234567890',
  alamat: 'Jl. Masjid No. 123, Jakarta Selatan',
  tanggalLahir: '1985-05-15',
  targetHaji: 35000000,
  tanggalTarget: '2026-12-31',
};

export default function ProfilScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  const [userData, setUserData] = useState(DUMMY_USER);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [targetModalVisible, setTargetModalVisible] = useState(false);
  const [editData, setEditData] = useState(DUMMY_USER);
  const [targetData, setTargetData] = useState({
    targetHaji: DUMMY_USER.targetHaji.toString(),
    tanggalTarget: DUMMY_USER.tanggalTarget,
  });

  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const formatTanggal = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  const handleSaveProfile = () => {
    setUserData(editData);
    setEditModalVisible(false);
    Alert.alert('Berhasil', 'Profil berhasil diperbarui');
  };

  const handleSaveTarget = () => {
    const newTarget = Number(targetData.targetHaji);
    if (newTarget <= 0) {
      Alert.alert('Error', 'Target haji harus lebih dari 0');
      return;
    }

    setUserData({
      ...userData,
      targetHaji: newTarget,
      tanggalTarget: targetData.tanggalTarget,
    });
    setTargetModalVisible(false);
    Alert.alert('Berhasil', 'Target haji berhasil diperbarui');
  };

  const handleLogout = () => {
    Alert.alert(
      'Logout',
      'Apakah Anda yakin ingin keluar dari aplikasi?',
      [
        { text: 'Batal', style: 'cancel' },
        { 
          text: 'Logout', 
          style: 'destructive',
          onPress: () => {
            Alert.alert('Logout', 'Anda telah keluar dari aplikasi');
          }
        }
      ]
    );
  };

  const menuItems = [
    {
      icon: 'person.fill',
      title: 'Edit Profil',
      subtitle: 'Ubah informasi pribadi',
      onPress: () => {
        setEditData(userData);
        setEditModalVisible(true);
      }
    },
    {
      icon: 'target',
      title: 'Edit Target',
      subtitle: 'Ubah target tabungan haji',
      onPress: () => {
        setTargetData({
          targetHaji: userData.targetHaji.toString(),
          tanggalTarget: userData.tanggalTarget,
        });
        setTargetModalVisible(true);
      }
    },
  ];

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <Text style={[styles.title, { color: colors.text }]}>
            Profil
          </Text>
        </View>

        {/* Profile Card */}
        <View style={[styles.profileCard, { backgroundColor: colors.tint }]}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <IconSymbol name="person.fill" size={32} color="white" />
            </View>
          </View>
          <Text style={styles.profileName}>{userData.nama}</Text>
          <Text style={styles.profileEmail}>{userData.email}</Text>
        </View>

        {/* Info Cards */}
        <View style={styles.section}>
          <View style={[styles.infoCard, { backgroundColor: colors.background }]}>
            <IconSymbol name="target" size={20} color={colors.tint} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.text }]}>
                Target Haji
              </Text>
              <Text style={[styles.infoValue, { color: colors.tint }]}>
                {formatRupiah(userData.targetHaji)}
              </Text>
            </View>
          </View>

          <View style={[styles.infoCard, { backgroundColor: colors.background }]}>
            <IconSymbol name="calendar" size={20} color={colors.tint} />
            <View style={styles.infoContent}>
              <Text style={[styles.infoLabel, { color: colors.text }]}>
                Target Tanggal
              </Text>
              <Text style={[styles.infoValue, { color: colors.tint }]}>
                {formatTanggal(userData.tanggalTarget)}
              </Text>
            </View>
          </View>
        </View>

        {/* Menu Items */}
        <View style={styles.section}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={[styles.menuItem, { backgroundColor: colors.background }]}
              onPress={item.onPress}
            >
              <View style={styles.menuLeft}>
                <View style={[styles.menuIcon, { backgroundColor: colors.tint + '20' }]}>
                  <IconSymbol name={item.icon as any} size={20} color={colors.tint} />
                </View>
                <View>
                  <Text style={[styles.menuTitle, { color: colors.text }]}>
                    {item.title}
                  </Text>
                  <Text style={[styles.menuSubtitle, { color: colors.text }]}>
                    {item.subtitle}
                  </Text>
                </View>
              </View>
              <IconSymbol name="chevron.right" size={16} color={colors.text} />
            </TouchableOpacity>
          ))}
        </View>

        {/* Logout Button */}
        <View style={styles.section}>
          <TouchableOpacity 
            style={[styles.logoutButton, { backgroundColor: '#ff4444' }]}
            onPress={handleLogout}
          >
            <Text style={styles.logoutText}>Logout</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>

      {/* Edit Profile Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={editModalVisible}
        onRequestClose={() => setEditModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Edit Profil
              </Text>
              <TouchableOpacity 
                onPress={() => setEditModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <ScrollView>
              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Nama</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.nama}
                  onChangeText={(text) => setEditData({...editData, nama: text})}
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Email</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.email}
                  onChangeText={(text) => setEditData({...editData, email: text})}
                  keyboardType="email-address"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Telepon</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.telepon}
                  onChangeText={(text) => setEditData({...editData, telepon: text})}
                  keyboardType="phone-pad"
                />
              </View>

              <View style={styles.inputContainer}>
                <Text style={[styles.inputLabel, { color: colors.text }]}>Alamat</Text>
                <TextInput
                  style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                  value={editData.alamat}
                  onChangeText={(text) => setEditData({...editData, alamat: text})}
                  multiline
                  numberOfLines={3}
                />
              </View>
            </ScrollView>

            <TouchableOpacity 
              style={[styles.submitButton, { backgroundColor: colors.tint }]}
              onPress={handleSaveProfile}
            >
              <Text style={styles.submitButtonText}>Simpan Perubahan</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>

      {/* Edit Target Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={targetModalVisible}
        onRequestClose={() => setTargetModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={[styles.modalContent, { backgroundColor: colors.background }]}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                Edit Target Haji
              </Text>
              <TouchableOpacity 
                onPress={() => setTargetModalVisible(false)}
                style={styles.closeButton}
              >
                <Text style={[styles.closeButtonText, { color: colors.text }]}>✕</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Target Biaya Haji (Rp)
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={targetData.targetHaji}
                onChangeText={(text) => setTargetData({...targetData, targetHaji: text})}
                keyboardType="numeric"
                placeholder="35000000"
              />
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.inputLabel, { color: colors.text }]}>
                Target Tanggal (YYYY-MM-DD)
              </Text>
              <TextInput
                style={[styles.input, { borderColor: colors.tint, color: colors.text }]}
                value={targetData.tanggalTarget}
                onChangeText={(text) => setTargetData({...targetData, tanggalTarget: text})}
                placeholder="2026-12-31"
              />
            </View>

            <TouchableOpacity 
              style={[styles.submitButton, { backgroundColor: colors.tint }]}
              onPress={handleSaveTarget}
            >
              <Text style={styles.submitButtonText}>Simpan Target</Text>
            </TouchableOpacity>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    padding: 20,
    paddingBottom: 10,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  profileCard: {
    margin: 20,
    padding: 24,
    borderRadius: 16,
    alignItems: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  profileName: {
    color: 'white',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  profileEmail: {
    color: 'white',
    fontSize: 14,
    opacity: 0.9,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 20,
  },
  infoCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoContent: {
    marginLeft: 12,
    flex: 1,
  },
  infoLabel: {
    fontSize: 12,
    opacity: 0.7,
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    marginBottom: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  menuLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  menuIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  menuTitle: {
    fontSize: 16,
    fontWeight: '500',
  },
  menuSubtitle: {
    fontSize: 12,
    opacity: 0.7,
    marginTop: 2,
  },
  logoutButton: {
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  logoutText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0,0,0,0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    width: '90%',
    maxHeight: '80%',
    borderRadius: 16,
    padding: 20,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  closeButton: {
    width: 30,
    height: 30,
    justifyContent: 'center',
    alignItems: 'center',
  },
  closeButtonText: {
    fontSize: 18,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 8,
  },
  input: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
  },
  submitButton: {
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 8,
  },
  submitButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});
