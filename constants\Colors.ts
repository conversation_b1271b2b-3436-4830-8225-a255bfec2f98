/**
 * Professional color system for the Tabungan Haji app
 * Designed for accessibility, readability, and modern aesthetics
 * Colors follow WCAG 2.1 AA contrast guidelines
 */

// Primary brand colors - Beautiful blue theme
const primaryLight = '#194a7a'; // Deep navy blue
const primaryDark = '#476f95';  // Medium blue for dark mode

// Secondary colors - Complementary blue tones
const secondaryLight = '#7593af'; // Light blue
const secondaryDark = '#a3b7ca';  // Lighter blue for dark mode

// Semantic colors - Harmonized with blue theme
const successLight = '#2E7D32'; // Green for success
const successDark = '#4CAF50';
const warningLight = '#F57C00'; // Orange for warning
const warningDark = '#FFB74D';
const errorLight = '#D32F2F'; // Red for error
const errorDark = '#EF5350';
const infoLight = '#7593af'; // Using our palette blue for info
const infoDark = '#a3b7ca';

// Neutral colors
const grayScale = {
  50: '#FAFAFA',
  100: '#F5F5F5',
  200: '#EEEEEE',
  300: '#E0E0E0',
  400: '#BDBDBD',
  500: '#9E9E9E',
  600: '#757575',
  700: '#616161',
  800: '#424242',
  900: '#212121',
};

export const Colors = {
  light: {
    // Text colors
    text: '#1A1A1A',
    textSecondary: '#666666',
    textMuted: '#999999',
    textInverse: '#FFFFFF',

    // Background colors
    background: '#d1dbe4', // Lightest blue from palette
    backgroundSecondary: '#FFFFFF',
    backgroundTertiary: '#a3b7ca', // Light blue from palette

    // Primary brand colors
    primary: primaryLight, // #194a7a
    primaryLight: '#476f95', // Medium blue
    primaryDark: '#194a7a', // Deep navy blue

    // Secondary colors
    secondary: secondaryLight, // #7593af
    secondaryLight: '#a3b7ca', // Lighter blue
    secondaryDark: '#476f95', // Medium blue

    // Semantic colors
    success: successLight,
    warning: warningLight,
    error: errorLight,
    info: infoLight,

    // UI element colors
    tint: primaryLight,
    border: grayScale[300],
    borderLight: grayScale[200],
    shadow: 'rgba(0, 0, 0, 0.1)',

    // Icon colors
    icon: grayScale[600],
    iconSecondary: grayScale[500],
    iconMuted: grayScale[400],

    // Tab colors
    tabIconDefault: grayScale[500],
    tabIconSelected: primaryLight,
    tabBackground: '#FFFFFF',

    // Card colors - Standardized card system
    cardBackground: 'rgba(255, 255, 255, 0.98)', // Primary card background
    cardBackgroundSecondary: '#a3b7ca', // Secondary card background (light blue)
    cardBackgroundPrimary: '#194a7a', // Primary colored cards (dark blue)
    cardBackgroundAccent: '#476f95', // Accent cards (medium blue)
    cardBorder: 'rgba(163, 183, 202, 0.25)', // Very subtle blue border
    cardBorderAccent: 'rgba(117, 147, 175, 0.4)', // More visible border for emphasis
    cardShadow: 'rgba(25, 74, 122, 0.08)', // Subtle blue shadow

    // Progress colors
    progressBackground: 'rgba(255, 255, 255, 0.3)', // White with transparency for better visibility
    progressFill: '#FFFFFF', // Pure white for maximum contrast on blue background

    // Surface colors
    surface: '#FFFFFF',
    surfaceSecondary: '#d1dbe4', // Lightest blue
    surfaceTertiary: '#a3b7ca', // Light blue

    // Special UI colors
    progressCardBackground: '#476f95', // Medium blue for progress card
    progressCardGradient: 'linear-gradient(135deg, #476f95 0%, #194a7a 100%)',
    motivationCardBackground: 'rgba(255, 255, 255, 0.9)',
    motivationCardBorder: 'rgba(163, 183, 202, 0.2)',
  },
  dark: {
    // Text colors
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textMuted: '#808080',
    textInverse: '#1A1A1A',

    // Background colors
    background: '#194a7a', // Deep navy blue
    backgroundSecondary: '#476f95', // Medium blue
    backgroundTertiary: '#7593af', // Light blue

    // Primary brand colors
    primary: primaryDark, // #476f95
    primaryLight: '#7593af', // Light blue
    primaryDark: '#194a7a', // Deep navy blue

    // Secondary colors
    secondary: secondaryDark, // #a3b7ca
    secondaryLight: '#d1dbe4', // Lightest blue
    secondaryDark: '#7593af', // Light blue

    // Semantic colors
    success: successDark,
    warning: warningDark,
    error: errorDark,
    info: infoDark,

    // UI element colors
    tint: primaryDark,
    border: '#404040',
    borderLight: '#333333',
    shadow: 'rgba(0, 0, 0, 0.3)',

    // Icon colors
    icon: '#B3B3B3',
    iconSecondary: '#999999',
    iconMuted: '#666666',

    // Tab colors
    tabIconDefault: '#a3b7ca',
    tabIconSelected: '#d1dbe4',
    tabBackground: '#476f95',

    // Card colors
    cardBackground: '#476f95', // Medium blue
    cardBorder: '#7593af', // Light blue
    cardShadow: 'rgba(25, 74, 122, 0.3)', // Blue shadow

    // Progress colors
    progressBackground: 'rgba(163, 183, 202, 0.3)', // Light blue background
    progressFill: '#d1dbe4', // Lightest blue for contrast

    // Surface colors
    surface: '#476f95', // Medium blue
    surfaceSecondary: '#7593af', // Light blue
    surfaceTertiary: '#a3b7ca', // Lighter blue
  },
};

// Typography scale
export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Spacing scale
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
};

// Border radius scale
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

// Shadow presets
export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
  },
};
