/**
 * Professional color system for the Tabungan Haji app
 * Designed for accessibility, readability, and modern aesthetics
 * Colors follow WCAG 2.1 AA contrast guidelines
 */

// Primary brand colors - Islamic green theme
const primaryLight = '#2D7D32'; // Deep forest green
const primaryDark = '#4CAF50';  // Lighter green for dark mode

// Secondary colors - Warm gold accents
const secondaryLight = '#F57C00'; // Warm orange-gold
const secondaryDark = '#FFB74D';  // Lighter gold for dark mode

// Semantic colors
const successLight = '#388E3C';
const successDark = '#66BB6A';
const warningLight = '#F57C00';
const warningDark = '#FFB74D';
const errorLight = '#D32F2F';
const errorDark = '#EF5350';
const infoLight = '#1976D2';
const infoDark = '#42A5F5';

// Neutral colors
const grayScale = {
  50: '#FAFAFA',
  100: '#F5F5F5',
  200: '#EEEEEE',
  300: '#E0E0E0',
  400: '#BDBDBD',
  500: '#9E9E9E',
  600: '#757575',
  700: '#616161',
  800: '#424242',
  900: '#212121',
};

export const Colors = {
  light: {
    // Text colors
    text: '#1A1A1A',
    textSecondary: '#666666',
    textMuted: '#999999',
    textInverse: '#FFFFFF',

    // Background colors
    background: '#FFFFFF',
    backgroundSecondary: '#FAFAFA',
    backgroundTertiary: '#F5F5F5',

    // Primary brand colors
    primary: primaryLight,
    primaryLight: '#4CAF50',
    primaryDark: '#1B5E20',

    // Secondary colors
    secondary: secondaryLight,
    secondaryLight: '#FFB74D',
    secondaryDark: '#E65100',

    // Semantic colors
    success: successLight,
    warning: warningLight,
    error: errorLight,
    info: infoLight,

    // UI element colors
    tint: primaryLight,
    border: grayScale[300],
    borderLight: grayScale[200],
    shadow: 'rgba(0, 0, 0, 0.1)',

    // Icon colors
    icon: grayScale[600],
    iconSecondary: grayScale[500],
    iconMuted: grayScale[400],

    // Tab colors
    tabIconDefault: grayScale[500],
    tabIconSelected: primaryLight,
    tabBackground: '#FFFFFF',

    // Card colors
    cardBackground: '#FFFFFF',
    cardBorder: grayScale[200],
    cardShadow: 'rgba(0, 0, 0, 0.08)',

    // Progress colors
    progressBackground: 'rgba(45, 125, 50, 0.1)',
    progressFill: primaryLight,

    // Surface colors
    surface: '#FFFFFF',
    surfaceSecondary: grayScale[50],
    surfaceTertiary: grayScale[100],
  },
  dark: {
    // Text colors
    text: '#FFFFFF',
    textSecondary: '#B3B3B3',
    textMuted: '#808080',
    textInverse: '#1A1A1A',

    // Background colors
    background: '#121212',
    backgroundSecondary: '#1E1E1E',
    backgroundTertiary: '#2A2A2A',

    // Primary brand colors
    primary: primaryDark,
    primaryLight: '#81C784',
    primaryDark: '#2E7D32',

    // Secondary colors
    secondary: secondaryDark,
    secondaryLight: '#FFCC02',
    secondaryDark: '#F57C00',

    // Semantic colors
    success: successDark,
    warning: warningDark,
    error: errorDark,
    info: infoDark,

    // UI element colors
    tint: primaryDark,
    border: '#404040',
    borderLight: '#333333',
    shadow: 'rgba(0, 0, 0, 0.3)',

    // Icon colors
    icon: '#B3B3B3',
    iconSecondary: '#999999',
    iconMuted: '#666666',

    // Tab colors
    tabIconDefault: '#999999',
    tabIconSelected: primaryDark,
    tabBackground: '#1E1E1E',

    // Card colors
    cardBackground: '#1E1E1E',
    cardBorder: '#333333',
    cardShadow: 'rgba(0, 0, 0, 0.2)',

    // Progress colors
    progressBackground: 'rgba(76, 175, 80, 0.2)',
    progressFill: primaryDark,

    // Surface colors
    surface: '#1E1E1E',
    surfaceSecondary: '#2A2A2A',
    surfaceTertiary: '#333333',
  },
};

// Typography scale
export const Typography = {
  // Font sizes
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
    '5xl': 48,
  },

  // Font weights
  fontWeight: {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
    extrabold: '800',
  },

  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },
};

// Spacing scale
export const Spacing = {
  xs: 4,
  sm: 8,
  md: 12,
  base: 16,
  lg: 20,
  xl: 24,
  '2xl': 32,
  '3xl': 40,
  '4xl': 48,
  '5xl': 64,
  '6xl': 80,
};

// Border radius scale
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  '2xl': 24,
  full: 9999,
};

// Shadow presets
export const Shadows = {
  sm: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
  },
  lg: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 12,
    elevation: 8,
  },
  xl: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 12,
  },
};
