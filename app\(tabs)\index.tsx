import { IconSymbol } from '@/components/ui/IconSymbol';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import { Alert, Dimensions, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Get screen dimensions
const { width: screenWidth } = Dimensions.get('window');

// Dummy data untuk simulasi
const DUMMY_DATA = {
  targetBiaya: 35000000, // 35 juta
  totalTabungan: 12500000, // 12.5 juta
  setoranTerakhir: 500000, // 500 ribu
  tanggalSetoranTerakhir: '2024-06-20',
  riwayatSetoran: [
    { id: 1, tanggal: '2024-06-20', jumlah: 500000 },
    { id: 2, tanggal: '2024-06-15', jumlah: 750000 },
    { id: 3, tanggal: '2024-06-10', jumlah: 600000 },
  ]
};

export default function BerandaScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const [data, setData] = useState(DUMMY_DATA);

  // Hitung progress persentase
  const progressPercentage = (data.totalTabungan / data.targetBiaya) * 100;

  // Estimasi waktu tercapai (asumsi setoran rata-rata per bulan)
  const rataRataSetoranPerBulan = 600000; // dari dummy data
  const sisaTabungan = data.targetBiaya - data.totalTabungan;
  const estimasiBulan = Math.ceil(sisaTabungan / rataRataSetoranPerBulan);

  // Calculate remaining amount needed
  const sisaAmount = data.targetBiaya - data.totalTabungan;
  
  const formatRupiah = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  const handleTambahSetoran = () => {
    Alert.alert(
      'Tambah Setoran',
      'Fitur ini akan mengarahkan ke halaman Tabungan untuk menambah setoran baru.',
      [{ text: 'OK' }]
    );
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >
        {/* Modern Header */}
        <View style={styles.header}>
          <View style={styles.headerContent}>
            <Text style={[styles.greeting, { color: colors.textSecondary }]}>
              Assalamu'alaikum
            </Text>
            <Text style={[styles.title, { color: colors.text }]}>
              Tabungan Haji Anda
            </Text>
          </View>
          <View style={[styles.headerIcon, { backgroundColor: colors.primary + '15' }]}>
            <IconSymbol name="person.circle.fill" size={32} color={colors.primary} />
          </View>
        </View>

        {/* Hero Progress Card */}
        <View style={[styles.heroCard, { backgroundColor: colors.primary }]}>
          <View style={styles.heroHeader}>
            <View style={styles.heroIconContainer}>
              <IconSymbol name="target" size={28} color="white" />
            </View>
            <View style={styles.heroTextContainer}>
              <Text style={styles.heroTitle}>Progress Tabungan</Text>
              <Text style={styles.heroSubtitle}>Menuju Tanah Suci</Text>
            </View>
          </View>

          <View style={styles.heroAmountContainer}>
            <Text style={styles.heroAmount}>
              {formatRupiah(data.totalTabungan)}
            </Text>
            <Text style={styles.heroTarget}>
              dari {formatRupiah(data.targetBiaya)}
            </Text>
          </View>

          {/* Enhanced Progress Bar */}
          <View style={styles.progressSection}>
            <View style={styles.progressBarContainer}>
              <View
                style={[
                  styles.progressBar,
                  { width: `${Math.min(progressPercentage, 100)}%` }
                ]}
              />
            </View>
            <View style={styles.progressInfo}>
              <Text style={styles.progressPercentage}>
                {progressPercentage.toFixed(1)}% tercapai
              </Text>
              <Text style={styles.progressRemaining}>
                Sisa: {formatRupiah(sisaAmount)}
              </Text>
            </View>
          </View>
        </View>

        {/* Enhanced Stats Grid */}
        <View style={styles.statsGrid}>
          <View style={[styles.statCard, { backgroundColor: colors.cardBackground }]}>
            <View style={[styles.statIconContainer, { backgroundColor: colors.info + '15' }]}>
              <IconSymbol name="calendar" size={24} color={colors.info} />
            </View>
            <View style={styles.statContent}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Estimasi Tercapai
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {estimasiBulan} bulan
              </Text>
            </View>
          </View>

          <View style={[styles.statCard, { backgroundColor: colors.cardBackground }]}>
            <View style={[styles.statIconContainer, { backgroundColor: colors.success + '15' }]}>
              <IconSymbol name="chart.bar.fill" size={24} color={colors.success} />
            </View>
            <View style={styles.statContent}>
              <Text style={[styles.statLabel, { color: colors.textSecondary }]}>
                Setoran Terakhir
              </Text>
              <Text style={[styles.statValue, { color: colors.text }]}>
                {formatRupiah(data.setoranTerakhir)}
              </Text>
            </View>
          </View>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsContainer}>
          <TouchableOpacity
            style={[styles.primaryAction, { backgroundColor: colors.primary }]}
            onPress={handleTambahSetoran}
          >
            <IconSymbol name="plus.circle.fill" size={24} color="white" />
            <Text style={styles.primaryActionText}>Tambah Setoran</Text>
          </TouchableOpacity>

          <View style={styles.secondaryActions}>
            <TouchableOpacity style={[styles.secondaryAction, { backgroundColor: colors.cardBackground }]}>
              <IconSymbol name="chart.line.uptrend.xyaxis" size={20} color={colors.primary} />
              <Text style={[styles.secondaryActionText, { color: colors.text }]}>Simulasi</Text>
            </TouchableOpacity>

            <TouchableOpacity style={[styles.secondaryAction, { backgroundColor: colors.cardBackground }]}>
              <IconSymbol name="doc.text" size={20} color={colors.primary} />
              <Text style={[styles.secondaryActionText, { color: colors.text }]}>Info Haji</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Motivational Section */}
        <View style={[styles.motivationSection, { backgroundColor: colors.cardBackground }]}>
          <View style={styles.motivationHeader}>
            <Text style={[styles.motivationTitle, { color: colors.text }]}>
              💪 Tetap Semangat!
            </Text>
            <Text style={[styles.motivationSubtitle, { color: colors.textSecondary }]}>
              Perjalanan menuju Tanah Suci
            </Text>
          </View>
          <Text style={[styles.motivationText, { color: colors.textSecondary }]}>
            Anda sudah menabung {progressPercentage.toFixed(1)}% dari target.
            Konsisten menabung akan membawa Anda lebih dekat ke Tanah Suci!
          </Text>
        </View>

        {/* Recent Transactions */}
        <View style={[styles.transactionsCard, { backgroundColor: colors.cardBackground }]}>
          <View style={styles.transactionsHeader}>
            <Text style={[styles.transactionsTitle, { color: colors.text }]}>
              Setoran Terbaru
            </Text>
            <TouchableOpacity>
              <Text style={[styles.viewAllText, { color: colors.primary }]}>
                Lihat Semua
              </Text>
            </TouchableOpacity>
          </View>

          {data.riwayatSetoran.slice(0, 3).map((item, index) => (
            <View key={item.id} style={[
              styles.transactionItem,
              index < 2 && { borderBottomWidth: 1, borderBottomColor: colors.border }
            ]}>
              <View style={styles.transactionLeft}>
                <View style={[styles.transactionIcon, { backgroundColor: colors.success + '15' }]}>
                  <IconSymbol name="arrow.up.circle.fill" size={16} color={colors.success} />
                </View>
                <View style={styles.transactionDetails}>
                  <Text style={[styles.transactionType, { color: colors.text }]}>
                    Setoran Tabungan
                  </Text>
                  <Text style={[styles.transactionDate, { color: colors.textSecondary }]}>
                    {new Date(item.tanggal).toLocaleDateString('id-ID', {
                      day: 'numeric',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </Text>
                </View>
              </View>
              <Text style={[styles.transactionAmount, { color: colors.success }]}>
                +{formatRupiah(item.jumlah)}
              </Text>
            </View>
          ))}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: 24,
  },

  // Header Styles
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    marginBottom: 8,
  },
  headerContent: {
    flex: 1,
  },
  greeting: {
    fontSize: 14,
    fontWeight: '400',
    marginBottom: 4,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    lineHeight: 32,
  },
  headerIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },

  // Hero Card Styles
  heroCard: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 24,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 16,
    elevation: 8,
  },
  heroHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  heroIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: 'rgba(255,255,255,0.2)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  heroTextContainer: {
    flex: 1,
  },
  heroTitle: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 2,
  },
  heroSubtitle: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    fontWeight: '400',
  },
  heroAmountContainer: {
    marginBottom: 24,
  },
  heroAmount: {
    color: 'white',
    fontSize: 32,
    fontWeight: '700',
    lineHeight: 40,
    marginBottom: 4,
  },
  heroTarget: {
    color: 'rgba(255,255,255,0.9)',
    fontSize: 16,
    fontWeight: '400',
  },

  // Progress Section Styles
  progressSection: {
    marginTop: 8,
  },
  progressBarContainer: {
    height: 12,
    backgroundColor: 'rgba(255,255,255,0.25)',
    borderRadius: 6,
    marginBottom: 16,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    backgroundColor: 'white',
    borderRadius: 6,
  },
  progressInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  progressPercentage: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
  progressRemaining: {
    color: 'rgba(255,255,255,0.8)',
    fontSize: 14,
    fontWeight: '400',
  },

  // Stats Grid Styles
  statsGrid: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    marginBottom: 24,
    gap: 16,
  },
  statCard: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  statContent: {
    flex: 1,
  },
  statLabel: {
    fontSize: 12,
    fontWeight: '500',
    marginBottom: 4,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  statValue: {
    fontSize: 16,
    fontWeight: '700',
    lineHeight: 20,
  },

  // Actions Styles
  actionsContainer: {
    paddingHorizontal: 20,
    marginBottom: 24,
  },
  primaryAction: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 24,
    borderRadius: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  primaryActionText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: 8,
  },
  secondaryActions: {
    flexDirection: 'row',
    gap: 12,
  },
  secondaryAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 1,
  },
  secondaryActionText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },

  // Motivation Section Styles
  motivationSection: {
    marginHorizontal: 20,
    marginBottom: 24,
    padding: 20,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  motivationHeader: {
    marginBottom: 12,
  },
  motivationTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  motivationSubtitle: {
    fontSize: 14,
    fontWeight: '500',
    opacity: 0.8,
  },
  motivationText: {
    fontSize: 14,
    lineHeight: 22,
    fontWeight: '400',
  },

  // Transactions Card Styles
  transactionsCard: {
    marginHorizontal: 20,
    marginBottom: 24,
    borderRadius: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  transactionsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 16,
  },
  transactionsTitle: {
    fontSize: 18,
    fontWeight: '700',
  },
  viewAllText: {
    fontSize: 14,
    fontWeight: '600',
  },
  transactionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  transactionLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  transactionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  transactionDetails: {
    flex: 1,
  },
  transactionType: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 2,
  },
  transactionDate: {
    fontSize: 12,
    fontWeight: '400',
  },
  transactionAmount: {
    fontSize: 16,
    fontWeight: '700',
  },

});
